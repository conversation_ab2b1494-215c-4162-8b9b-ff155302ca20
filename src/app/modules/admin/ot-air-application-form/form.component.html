<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto bg-card m-4 p-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
      <div>
        <h2 class=" mb-2 truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Overtime Air Request
        </h2>
      </div>
    </div>
    <div class="flex flex-row justify-center pb-2 my-5 w-full">
      <form class="overflow-hidden px-0 md:px-8  mb-4 flex flex-col w-full" [formGroup]="form">
        <div class="flex flex-col md:flex-row gap-4 justify-center w-full">
          <div class="flex flex-col w-full md:w-full">
            <div class="-mx-3 md:flex mb-0 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                      <mat-label>Employee</mat-label>
                      <input
                          matInput
                          [formControl]="employeeFilter"
                          [matAutocomplete]="employeeAutoComplete"
                          placeholder="Search Employee"
                      />
                      <mat-autocomplete
                          #employeeAutoComplete="matAutocomplete"
                          (optionSelected)="onSelectEmployer($event.option.value, 'manual')">
                          <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                              {{item.firstname}} {{item.lastname}}
                          </mat-option>
                      </mat-autocomplete>
                  </mat-form-field>
              </div>
          </div>
            <div class="-mx-3 md:flex mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Project</mat-label>
                  <input matInput [formControl]="projectFilter" [matAutocomplete]="projectAutoComplete"
                    placeholder="Search Project" />
                  <mat-autocomplete #projectAutoComplete="matAutocomplete"
                    (optionSelected)="onSelectProject($event.option.value, 'manual')">
                    <mat-option *ngFor="let item of filterProject | async" [value]="item">
                      ({{item.code}}) {{item.name}}
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Overtime Date</mat-label>
                  <input matInput [matDatepicker]="picker" placeholder="Select Date" formControlName="date">
                  <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <div class="flex items-center gap-2">
                  <!-- Start Time -->
                  <mat-form-field class="w-full">
                    <mat-label>Time From</mat-label>
                    <mat-select placeholder="Please select Start Time" [formControl]="form.controls['timeStart']"
                      (selectionChange)="onStartTimeChange()">
                      <mat-option *ngFor="let time of timeOptions" [value]="time">
                        {{ time }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <!-- End Time -->
                  <mat-form-field class="w-full">
                    <mat-label>To</mat-label>
                    <mat-select placeholder="Please select End Time" [formControl]="form.controls['timeEnd']"
                      [disabled]="!form.controls['timeStart'].value" (selectionChange)="calculateDuration()">
                      <mat-option *ngFor="let time of timeOptions" [value]="time">
                        {{ time }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-0 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                  <mat-label>Total Hours</mat-label>
                  <input matInput [value]="duration" placeholder="" readonly />
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-0 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                  <mat-label>Review By</mat-label>
                  <input matInput [formControl]="approverFilter" [matAutocomplete]="approverAutoComplete"
                    placeholder="Search Approver" />
                  <mat-autocomplete #approverAutoComplete="matAutocomplete"
                    (optionSelected)="onSelectApprover($event.option.value, 'manual')">
                    <mat-option *ngFor="let item of filterApprover | async" [value]="item">
                      {{item.firstname}} {{item.lastname}}
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-0 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                  <mat-label>Email</mat-label>
                  <input matInput formControlName="email" placeholder="" />
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="flex flex-col w-full md:w-full">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Detail of work</mat-label>
                  <textarea matInput [rows]="6" formControlName="detail"></textarea>
                  <mat-hint>(Use 200 character maximum.)</mat-hint>
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-0 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Zone</mat-label>
                  <mat-select placeholder="Please Select" formControlName="zoneId">
                    <mat-option *ngFor="let item of ZoneData;" value="{{item.id}}">
                      {{item?.name}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-6 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-label>Zone Detail</mat-label>
                <div class="flex flex-wrap gap-2 pt-2">
                  <button *ngFor="let item of FloorData" type="button" class="btn btn-primary"
                    [class.selected]="form.controls['floorId'].value === item.id"
                    (click)="selectFloor(item.pic,item.id)">
                    {{ item?.name }}
                  </button>

                </div>
                <div>

                </div>


              </div>
            </div>
            <div class="-mx-3 md:flex mb-6 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <div class="flex justify-center items-center">
                  <!-- ใส่ class สำหรับ responsive -->
                  <img [src]="imgSelected" alt="Selected Floor" class="responsive-img" />
                </div>
              </div>
            </div>

          </div>
        </div>
        <div class="flex flex-row mt-2 justify-center">
          <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
              Process
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
              Cancel
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
<style>
  button.selected {
    background-color: #007bff;
    color: #fff;
    border: 2px solid #0056b3;
  }

  button {
    padding: 10px 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    cursor: pointer;
    background-color: #f8f9fa;
  }

  button:hover {
    background-color: #e9ecef;
  }

.responsive-img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}


@media (max-width: 768px) {
  .responsive-img {
    max-width: 90%;
  }
}

</style>
