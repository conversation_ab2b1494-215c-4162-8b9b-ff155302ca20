<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-4 m-4 rounded-md sm:p-10 bg-card">
    <div class="flex flex-col justify-between gap-2 pb-2 mb-5 border-b-2 border-gray-300 md:flex-row">
      <div>
        <h2 class="text-3xl font-extrabold leading-7 tracking-tight truncate sm:leading-10 md:text-4xl">
          OT Request Report Group By Project Owner
        </h2>
      </div>
      <div class="flex flex-col justify-end gap-2 pb-2 my-2 md:flex-row">
        <button mat-flat-button [color]="'primary'" (click)="this.GetReport()">
          <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
          <span class="ml-2">Search</span>
        </button>
        <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
          <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
          <span class="ml-2">Reset</span>
        </button>
        <button mat-flat-button [color]="'primary'" (click)="exportExcel()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cloud-arrow-down'"></mat-icon>
          <span class="ml-2">Download</span>
        </button>
      </div>
    </div>
    <form [formGroup]="form">
      <div class="flex flex-col justify-start gap-2 my-2 border-b-2 border-gray-300 md:flex-row">
        <div
          class="items-end w-full text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10 ">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
            <mat-label>Start - End</mat-label>
            <mat-date-range-input [rangePicker]="picker1">
              <input matStartDate formControlName="dateStart" placeholder="Start" (dateChange)="dateChange()">
              <input matEndDate formControlName="dateEnd" placeholder="End" (dateChange)="dateChange()">
            </mat-date-range-input>
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-date-range-picker #picker1></mat-date-range-picker>
          </mat-form-field>
        </div>
        <div
          class="items-end w-full text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
            <mat-label>Employee</mat-label>
            <input matInput [formControl]="employeeFilter" [matAutocomplete]="AutoCompleteemployee" placeholder="Search Employee"
              (selectionChange)="GetReport()" />
            <mat-autocomplete #AutoCompleteemployee="matAutocomplete"
              (optionSelected)="onSelectemployee($event.option.value, 'manual')">
              <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                {{item.firstname}} {{item.lastname}}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
        <div
          class="items-end w-full text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10 ">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
            <mat-label>Project</mat-label>
            <input matInput [formControl]="projectFilter" [matAutocomplete]="AutoCompleteProject" placeholder="Search project"
              (selectionChange)="GetReport()" />
            <mat-autocomplete #AutoCompleteProject="matAutocomplete"
              (optionSelected)="onSelectproject($event.option.value, 'manual')">
              <mat-option *ngFor="let item of filterproject | async" [value]="item">
                [{{item.code}}] {{ item.name }}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
        <div
          class="items-end w-full text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10 ">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
            <mat-label>Status</mat-label>
            <mat-select [formControlName]="'status'" placeholder="Select status">
                <mat-option *ngFor="let item of this.status;" value="{{item.value}}">
                    {{item.name}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        </div>
      </div>
    </form>
    <div class="overflow-auto">
      <table id="excel-table"
        class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
        <thead class="text-gray-700 uppercase bg-gray-200 text-md dark:bg-gray-700 dark:text-gray-400">
          <tr class="border-[1px] border-black">
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">RequestID</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Fullname</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Request Date</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">OT Date</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">StartTime</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">EndTime</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Hours</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Project</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Detail</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Reviewer</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Approver</th>
            <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Status</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of items">
            <!-- Row for Department -->
            <tr class="" [ngClass]="{'bg-red-200': item?.isHoliday}">
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.code ?? ''}}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.employee?.firstname ?? '' }} {{ item?.employee?.lastname ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ (item?.createdAt | date : 'dd/MM/yyyy') ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ (item?.date | date : 'dd/MM/yyyy') ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.timeStart  ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.timeEnd  ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.qtyHour  ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.project?.code  ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.detail  ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.head?.firstname  ?? '' }} {{ item?.head?.lastname  ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ item?.approver?.firstname  ?? '' }} {{ item?.approver?.lastname  ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{checkStatus(item?.status)}}
              </td>
            </tr>
          </ng-container>
          <tr>
            <td class="px-2 py-2 text-md border-[1px] border-gray-900 bg-blue-100 text-end font-bold" colspan="6">
              Total
            </td>
            <td class="px-2 py-2 text-md border-[1px] border-gray-900 bg-blue-100 font-bold" colspan="6">
              {{ this.totalSum }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>Edit</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>Delete</span>
    </button>
  </mat-menu>
</ng-template>
